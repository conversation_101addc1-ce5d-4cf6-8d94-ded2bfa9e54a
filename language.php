<?php
// Language configuration for Najvagram Bot
// Supported languages: Persian (fa), English (en)

$languages = [
    'fa' => [
        // Main menu messages
        'welcome_message' => "سلام {name} 👋

به ربات نجوا گرام خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",

        // Buttons
        'btn_anonymous_message' => '✨ پیام ناشناس',
        'btn_najva_section' => '💬 بخش نجوا',
        'btn_support' => '☎️ پشتیبانی',
        'btn_help' => '📚 راهنما',
        'btn_privacy' => '👀 حریم خصوصی',
        'btn_language' => '🌐 زبان',
        'btn_cancel' => '❌ لغو',
        'btn_main_menu' => '🏠 منوی اصلی',
        'btn_view_sample' => '👀 مشاهده نمونه',
        'btn_send_najva' => 'ارسال نجوا به {name}',
        'btn_privacy_settings' => '🔧 تنظیمات حریم خصوصی',
        'btn_enable' => '✅ فعال کردن',
        'btn_disable' => '❌ غیرفعال کردن',
        'btn_show_message' => '🧐 نمایش پیام',

        // Language selection
        'language_selection' => "🌐 انتخاب زبان

لطفاً زبان مورد نظر خود را انتخاب کنید:",
        'btn_persian' => '🇮🇷 فارسی',
        'btn_english' => '🇺🇸 English',
        'language_changed' => "✅ زبان با موفقیت به فارسی تغییر یافت",

        // Send message section
        'send_message_text' => "✨ نجوا خصوصی

برای ارسال نجوا (پیام خصوصی) به شخص مورد نظر شما میتوانید با استفاده از موارد زیر اقدام کنید :

1. فوروارد یک پیام از کاربر (در صورتی که ربات را استارت کرده باشد).
2. ارسال آیدی عددی کاربر (اگر نمیدانید ایدی عددی چیست به قسمت راهنما رجوع کنید).
3. ارسال یوزرنیم کاربر (مخصوص مواقعی که دسترسی به پیام یا ایدی شخص مورد نظر ندارید و یوزرنیم فقط جهت تگ کردن کاربر است).

😉 حالا یکی از موارد بالا را انتخاب کنید و ارسال کنید.",

        // Help section
        'help_text' => "📚 راهنما

برای استفاده از ربات به صورت از راه دور شما میتوانید از دو روش اقدام کنید 👇

1. جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس یوزرنیم گیرنده قرار دهید.
مثال :
<code>@Najvagram_Bot سلام @Ali</code>

2.جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس ایدی عددی گیرنده قرار دهید.
مثال :
<code>@Najvagram_Bot سلام USER_ID(آیدی عددی فرد)</code>",

        // Privacy section
        'privacy_text' => "👀 تنظیمات حریم‌خصوصی

<b>✱ وضعیت فعلی: {status}</b>

✱ با فعال کردن حریم خصوصی، هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.

<blockquote>⚠️ توجه: در صورت فعال بودن حریم خصوصی، تمام پیام‌های ناشناس ارسالی به شما مسدود خواهد شد.</blockquote>",
        'privacy_enabled' => "✅ حریم خصوصی فعال شد

از این پس هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.

برای غیرفعال کردن مجدداً به این بخش مراجعه کنید.",
        'privacy_disabled' => "❌ حریم خصوصی غیرفعال شد

از این پس دیگران می‌توانند برای شما پیام ناشناس ارسال کنند.

برای فعال کردن مجدداً به این بخش مراجعه کنید.",
        'privacy_status_on' => 'فعال',
        'privacy_status_off' => 'غیرفعال',

        // Support section
        'support_text' => "🤳 پشتیبانی

✱ برای ارتباط با پشتیبانی و گزارش مشکلات می‌توانید از راه‌های زیر اقدام کنید:

🛃 آیدی پشتیبانی: @Sia32vosh

⏰ ساعات پاسخگویی: 9 صبح تا 9 شب

<blockquote>لطفاً مشکل خود را به صورت کامل شرح دهید تا بتوانیم بهترین کمک را به شما ارائه دهیم.</blockquote>",

        // Error messages
        'user_info_error' => "❌ خطا در دریافت اطلاعات کاربر

متأسفانه امکان دریافت اطلاعات این کاربر وجود ندارد.

لطفاً از کاربر مورد نظر بخواهید ابتدا ربات را استارت کند سپس مجدداً تلاش نمایید.",
        'user_not_found' => "اوپس 😲

اطلاعات کاربر دریافت نشد !
از او بخواهید ربات را استارت بزند سپس مجدد تلاش کنید ☹️",

        // User info
        'user_info' => "اطلاعات کاربر به شرح زیر میباشد :

•• آیدی عددی ~> {id}
•• یوزرنیم ~> @{username}
•• اسم ~> {name}",
        'username_info' => "✨ اطلاعات کاربر به شرح زیر میباشد :

💬 یوزرنیم وارد شده :
{username}",

        // Inline messages
        'inline_title' => "برای ارسال نجوا اینجا ضربه بزنید ❗️",
        'inline_description_username' => "ارسال نجوا (پیام مخفی) به {user}\nاز @ در متن خود استفاده نکنید !",
        'inline_description_id' => "ارسال نجوا (پیام مخفی) به {name}\nاز # در متن خود استفاده نکنید !",
        'inline_message_username' => "کاربر { {user} } شما یک پیام از طرف ( {sender} ) دارید !",
        'inline_message_id' => "کاربر { {name} | @{username} } شما یک پیام از طرف ({sender}) دارید !",
        'privacy_blocked_title' => "❌ امکان ارسال نجوا وجود ندارد",
        'privacy_blocked_description' => "{name} حریم خصوصی خود را فعال کرده است",
        'privacy_blocked_message' => "⚠️ کاربر {name} حریم خصوصی خود را فعال کرده و امکان دریافت پیام ناشناس ندارد.",
        'not_for_you' => "کاربر عزیز ! این نجوا برای شما نیست 🤕",
    ],

    'en' => [
        // Main menu messages
        'welcome_message' => "Hello {name} 👋

Welcome to Najvagram Bot!

Using this bot, you and your friends can send anonymous messages.

<blockquote>📚 Please read the usage instructions through the help button or /help command.</blockquote>",

        // Buttons
        'btn_anonymous_message' => '✨ Anonymous Message',
        'btn_najva_section' => '💬 Najva Section',
        'btn_support' => '☎️ Support',
        'btn_help' => '📚 Help',
        'btn_privacy' => '👀 Privacy',
        'btn_language' => '🌐 Language',
        'btn_cancel' => '❌ Cancel',
        'btn_main_menu' => '🏠 Main Menu',
        'btn_view_sample' => '👀 View Sample',
        'btn_send_najva' => 'Send Najva to {name}',
        'btn_privacy_settings' => '🔧 Privacy Settings',
        'btn_enable' => '✅ Enable',
        'btn_disable' => '❌ Disable',
        'btn_show_message' => '🧐 Show Message',

        // Language selection
        'language_selection' => "🌐 Language Selection

Please select your preferred language:",
        'btn_persian' => '🇮🇷 فارسی',
        'btn_english' => '🇺🇸 English',
        'language_changed' => "✅ Language successfully changed to English",

        // Send message section
        'send_message_text' => "✨ Private Najva

To send a najva (private message) to the desired person, you can use the following methods:

1. Forward a message from the user (if they have started the bot).
2. Send the user's numeric ID (if you don't know what a numeric ID is, refer to the help section).
3. Send the user's username (for cases where you don't have access to the person's message or ID and the username is only for tagging the user).

😉 Now choose one of the above options and send it.",

        // Help section
        'help_text' => "📚 Help

To use the bot remotely, you can use two methods 👇

1. To send, you must use inline. Just type the bot's username ( @Najvagram_Bot ) in the chat first, then a space, then the text, then the recipient's username.
Example:
<code>@Najvagram_Bot Hello @Ali</code>

2. To send, you must use inline. Just type the bot's username ( @Najvagram_Bot ) in the chat first, then a space, then the text, then the recipient's numeric ID.
Example:
<code>@Najvagram_Bot Hello USER_ID(person's numeric ID)</code>",

        // Privacy section
        'privacy_text' => "👀 Privacy Settings

<b>✱ Current Status: {status}</b>

✱ By enabling privacy, no one can send you anonymous messages.

<blockquote>⚠️ Note: If privacy is enabled, all anonymous messages sent to you will be blocked.</blockquote>",
        'privacy_enabled' => "✅ Privacy enabled

From now on, no one can send you anonymous messages.

To disable, visit this section again.",
        'privacy_disabled' => "❌ Privacy disabled

From now on, others can send you anonymous messages.

To enable, visit this section again.",
        'privacy_status_on' => 'Enabled',
        'privacy_status_off' => 'Disabled',

        // Support section
        'support_text' => "🤳 Support

✱ To contact support and report issues, you can use the following methods:

🛃 Support ID: @Sia32vosh

⏰ Response hours: 9 AM to 9 PM

<blockquote>Please describe your problem completely so we can provide you with the best help.</blockquote>",

        // Error messages
        'user_info_error' => "❌ Error retrieving user information

Unfortunately, it is not possible to retrieve information for this user.

Please ask the desired user to start the bot first, then try again.",
        'user_not_found' => "Oops 😲

User information not received!
Ask them to start the bot then try again ☹️",

        // User info
        'user_info' => "User information is as follows:

•• Numeric ID ~> {id}
•• Username ~> @{username}
•• Name ~> {name}",
        'username_info' => "✨ User information is as follows:

💬 Entered username:
{username}",

        // Inline messages
        'inline_title' => "Click here to send najva ❗️",
        'inline_description_username' => "Send najva (hidden message) to {user}\nDo not use @ in your text!",
        'inline_description_id' => "Send najva (hidden message) to {name}\nDo not use # in your text!",
        'inline_message_username' => "User { {user} } you have a message from ( {sender} )!",
        'inline_message_id' => "User { {name} | @{username} } you have a message from ({sender})!",
        'privacy_blocked_title' => "❌ Cannot send najva",
        'privacy_blocked_description' => "{name} has enabled their privacy",
        'privacy_blocked_message' => "⚠️ User {name} has enabled their privacy and cannot receive anonymous messages.",
        'not_for_you' => "Dear user! This najva is not for you 🤕",
    ]
];

// Function to get text based on user's language
function getText($key, $user_lang = 'fa', $replacements = []) {
    global $languages;

    // Default to Persian if language not supported
    if (!isset($languages[$user_lang])) {
        $user_lang = 'fa';
    }

    // Get the text
    $text = isset($languages[$user_lang][$key]) ? $languages[$user_lang][$key] : $languages['fa'][$key];

    // Replace placeholders
    foreach ($replacements as $placeholder => $value) {
        $text = str_replace('{' . $placeholder . '}', $value, $text);
    }

    return $text;
}

// Function to get user's language preference
function getUserLanguage($user_id) {
    $user_file = "data/$user_id.json";
    if (file_exists($user_file)) {
        $user_data = json_decode(file_get_contents($user_file), true);
        return isset($user_data['language']) ? $user_data['language'] : 'fa';
    }
    return 'fa'; // Default to Persian
}

// Function to set user's language preference
function setUserLanguage($user_id, $language) {
    update_user_data("data/$user_id.json", "language", $language);
}
?>