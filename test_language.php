<?php
// Test script for language functionality
require_once 'language.php';

echo "Testing Language System:\n\n";

// Test Persian (default)
echo "=== Persian (fa) ===\n";
echo "Welcome message: " . getText('welcome_message', 'fa', ['name' => 'علی']) . "\n\n";
echo "Language button: " . getText('btn_language', 'fa') . "\n";
echo "Help button: " . getText('btn_help', 'fa') . "\n\n";

// Test English
echo "=== English (en) ===\n";
echo "Welcome message: " . getText('welcome_message', 'en', ['name' => 'Ali']) . "\n\n";
echo "Language button: " . getText('btn_language', 'en') . "\n";
echo "Help button: " . getText('btn_help', 'en') . "\n\n";

// Test language selection
echo "=== Language Selection ===\n";
echo "Persian selection: " . getText('language_selection', 'fa') . "\n\n";
echo "English selection: " . getText('language_selection', 'en') . "\n\n";

// Test user language functions
echo "=== User Language Functions ===\n";
echo "Default language for new user: " . getUserLanguage(12345) . "\n";

// Create test user data
if (!is_dir("data")) {
    mkdir("data", 0777, true);
}
file_put_contents("data/12345.json", json_encode(["step" => "None", "privacy" => "off", "language" => "en"]));

echo "Language for user with English preference: " . getUserLanguage(12345) . "\n";

// Clean up test file
unlink("data/12345.json");

echo "\nLanguage system test completed successfully!\n";
?>
